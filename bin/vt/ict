#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

# shellcheck disable=SC1091
source "$DOT_DIR/share/fns"

usage() {
    cat <<EOF
Usage: $0 <command> [container-name] [args...]

Manage Incus LXC containers similar to other container management tools.

COMMANDS:
    list                    List all Incus containers
    status <name>           Show container status and info
    create <distro> [name]  Create a new Incus container
    start <name>            Start a container
    stop <name>             Stop a container
    restart <name>          Restart a container
    delete <name>           Delete a container completely
    shell <name>            Enter container shell
    exec <name> <cmd>       Execute command in container
    ip <name>               Get container IP address
    ssh <name> [username]   Connect to container via SSH
    info <name>             Show detailed container information
    config <name>           Show container configuration
    logs <n> [type]      Show container logs (instance, console, or system)
    snapshot <name> [snap]  Create container snapshot
    restore <name> <snap>   Restore container from snapshot
    copy <src> <dest>       Copy container
    cleanup                 Remove stopped containers

SUPPORTED DISTROS:
    ubuntu, fedora, arch, tumbleweed, debian, centos, alpine

EXAMPLES:
    $0 list                         # List all containers
    $0 status ubuntu                # Show status of 'ubuntu' container
    $0 create ubuntu myubuntu       # Create Ubuntu container named 'myubuntu'
    $0 create fedora                # Create Fedora container with default name
    $0 shell ubuntu                 # Enter 'ubuntu' container shell
    $0 exec ubuntu "ls -la"         # Run command in 'ubuntu' container
    $0 ip ubuntu                    # Get IP address of 'ubuntu' container
    $0 ssh ubuntu                   # SSH to 'ubuntu' container (auto-detect username)
    $0 ssh ubuntu root              # SSH to 'ubuntu' container as 'root' user
    $0 logs ubuntu                  # Show instance logs for 'ubuntu' container
    $0 logs ubuntu console          # Show console logs for 'ubuntu' container
    $0 logs ubuntu system           # Show system logs for 'ubuntu' container
    $0 snapshot ubuntu backup       # Create snapshot named 'backup'
    $0 delete old-container         # Delete 'old-container' completely

EOF
}

check_incus() {
    if ! has_cmd incus; then
        fail "incus command not found. Please install Incus first."
        exit 1
    fi
}

list_containers() {
    check_incus || return 1

    slog "Listing all Incus containers..."
    echo

    incus list type=container
}

get_container_state() {
    incus list type=container "$container_name" --format csv --columns s | head -1
}

has_container() {
    local container_name="$1"
    incus list type=container --format csv --columns n | grep -q "^${container_name}$" || return 1
}

check_container() {
    local container_name="$1"
    if ! has_container "$container_name"; then
        fail "Container '$container_name' not found"
        return 1
    fi
}

container_status() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name"

    slog "Status for container '$container_name':"
    echo
    incus list type=container "$container_name"
    echo

    slog "Detailed information:"
    incus info "$container_name" | head -20
}

create_container() {
    local distro="$1"
    local container_name="${2:-$distro}"
    check_incus || return 1
    check_container "$container_name" || return 1

    slog "Creating $distro Incus container: $container_name"

    # Check if function exists for the distro
    if declare -f "incus-${distro}-lxc" >/dev/null 2>&1; then
        # Use existing function but with custom name
        local temp_name="$distro"
        "incus-${distro}-lxc"

        # Rename if custom name provided and different from default
        if [[ "$container_name" != "$distro" ]]; then
            slog "Renaming container from '$temp_name' to '$container_name'..."
            incus move "$temp_name" "$container_name" 2>/dev/null || true
        fi
    else
        # Fallback to direct incus launch
        case "$distro" in
        ubuntu)
            incus launch images:ubuntu/24.04 "$container_name"
            ;;
        fedora)
            incus launch images:fedora/42 "$container_name"
            ;;
        arch | archlinux)
            incus launch images:archlinux/current "$container_name"
            ;;
        tumbleweed | tw)
            incus launch images:opensuse/tumbleweed "$container_name"
            ;;
        debian)
            incus launch images:debian/12 "$container_name"
            ;;
        centos)
            incus launch images:centos/9-Stream "$container_name"
            ;;
        alpine)
            incus launch images:alpine/3.19 "$container_name"
            ;;
        nixos)
            incus launch images:nixos/unstable "$container_name" --config security.nesting=true
            ;;
        *)
            fail "Unsupported distro: $distro"
            slog "Supported distros: ubuntu, fedora, arch, tumbleweed, debian, centos, alpine, nixos"
            return 1
            ;;
        esac
    fi

    if incus info "$container_name" >/dev/null 2>&1; then
        success "Container '$container_name' created successfully"
        slog "Starting container..."
        incus start "$container_name" 2>/dev/null || true
        sleep 2
        slog "Container status:"
        incus list type=container "$container_name"
    else
        fail "Failed to create container '$container_name'"
        return 1
    fi
}

start_container() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" == "RUNNING" ]]; then
        warn "Container '$container_name' is already running"
        return 0
    fi

    slog "Starting container '$container_name'..."
    if incus start "$container_name"; then
        success "Container '$container_name' started"
        sleep 1
        incus list type=container "$container_name"
    else
        fail "Failed to start container '$container_name'"
        return 1
    fi
}

stop_container() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" != "RUNNING" ]]; then
        warn "Container '$container_name' is not running"
        return 0
    fi

    slog "Stopping container '$container_name'..."
    if incus stop "$container_name"; then
        success "Container '$container_name' stopped"
    else
        fail "Failed to stop container '$container_name'"
        return 1
    fi
}

restart_container() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    slog "Restarting container '$container_name'..."
    if incus restart "$container_name"; then
        success "Container '$container_name' restarted"
        sleep 2
        incus list type=container "$container_name"
    else
        fail "Failed to restart container '$container_name'"
        return 1
    fi
}

delete_container() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    warn "This will permanently delete container '$container_name' and all its data!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Deletion cancelled"
        return 0
    fi

    local state
    state=$(get_container_state "$container_name")
    if [[ "$state" == "RUNNING" ]]; then
        slog "Stopping container first..."
        incus stop "$container_name" --force
    fi

    slog "Deleting container '$container_name'..."
    if incus delete "$container_name"; then
        success "Container '$container_name' deleted successfully"
    else
        fail "Failed to delete container '$container_name'"
        return 1
    fi
}

enter_shell() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "Container '$container_name' is not running"
        slog "Start it with: $0 start $container_name"
        return 1
    fi

    slog "Entering shell of container '$container_name'..."
    incus exec "$container_name" -- /bin/bash -l
}

exec_in_container() {
    local container_name="$1"
    shift
    local command="$*"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "Container '$container_name' is not running"
        slog "Start it with: $0 start $container_name"
        return 1
    fi

    slog "Executing command in container '$container_name': $command"
    incus exec "$container_name" -- "$@"
}

show_container_info() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    slog "Detailed information for container '$container_name':"
    echo
    incus info "$container_name"
}

show_container_config() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    slog "Configuration for container '$container_name':"
    echo
    incus config show "$container_name"
}

show_logs() {
    local container_name="$1"
    local log_type="${2:-instance}"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    case "$log_type" in
    instance)
        slog "Showing instance logs for container '$container_name'..."
        echo
        incus info "$container_name" --show-log
        ;;
    console)
        slog "Showing console logs for container '$container_name'..."
        echo
        incus console "$container_name" --show-log
        ;;
    system)
        if [[ "$state" != "RUNNING" ]]; then
            fail "Container '$container_name' is not running"
            slog "Start it with: $0 start $container_name"
            return 1
        fi

        slog "Showing system logs for container '$container_name'..."
        echo

        slog "=== Recent system messages (journalctl) ==="
        incus exec "$container_name" -- journalctl --no-pager -n 50 2>/dev/null || {
            slog "=== System log (/var/log/syslog) ==="
            incus exec "$container_name" -- tail -50 /var/log/syslog 2>/dev/null || {
                slog "=== System messages (/var/log/messages) ==="
                incus exec "$container_name" -- tail -50 /var/log/messages 2>/dev/null || {
                    warn "Could not read system logs - container may not have systemd or standard log files"
                }
            }
        }
        ;;
    *)
        fail "Unknown log type: $log_type"
        slog "Available log types: instance, console, system"
        return 1
        ;;
    esac
}

create_snapshot() {
    local container_name="$1"
    local snapshot_name="${2:-snap-$(date +%Y%m%d-%H%M%S)}"
    check_incus || return 1
    check_container "$container_name" || return 1

    slog "Creating snapshot '$snapshot_name' for container '$container_name'..."
    if incus snapshot "$container_name" "$snapshot_name"; then
        success "Snapshot '$snapshot_name' created successfully"
    else
        fail "Failed to create snapshot '$snapshot_name'"
        return 1
    fi
}

restore_snapshot() {
    local container_name="$1"
    local snapshot_name="$2"
    check_incus || return 1
    check_container "$container_name" || return 1

    if [[ -z "$snapshot_name" ]]; then
        fail "Snapshot name required"
        return 1
    fi

    warn "This will restore container '$container_name' to snapshot '$snapshot_name'"
    warn "All changes since the snapshot will be lost!"
    read -p "Are you sure? (y/N): " -r
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        slog "Restore cancelled"
        return 0
    fi

    slog "Restoring container '$container_name' from snapshot '$snapshot_name'..."
    if incus restore "$container_name" "$snapshot_name"; then
        success "Container '$container_name' restored from snapshot '$snapshot_name'"
    else
        fail "Failed to restore container '$container_name' from snapshot '$snapshot_name'"
        return 1
    fi
}

copy_container() {
    local source_container="$1"
    local dest_container="$2"
    check_incus || return 1
    check_container "$source_container" || return 1
    check_container "$dest_container" && {
        fail "Destination container '$dest_container' already exists"
        return 1
    }

    if [[ -z "$dest_container" ]]; then
        fail "Destination container name required"
        return 1
    fi

    if ! incus info "$source_container" >/dev/null 2>&1; then
        fail "Source container '$source_container' not found"
        return 1
    fi

    if incus info "$dest_container" >/dev/null 2>&1; then
        fail "Destination container '$dest_container' already exists"
        return 1
    fi

    slog "Copying container '$source_container' to '$dest_container'..."
    if incus copy "$source_container" "$dest_container"; then
        success "Container '$source_container' copied to '$dest_container'"
    else
        fail "Failed to copy container '$source_container' to '$dest_container'"
        return 1
    fi
}

cleanup_containers() {
    check_incus || return 1

    slog "Cleaning up stopped containers..."

    # List stopped containers
    local stopped_containers
    stopped_containers=$(incus list --format csv --columns n,s | grep ",STOPPED$" | cut -d',' -f1)

    if [[ -n "$stopped_containers" ]]; then
        slog "Stopped containers found:"
        echo "$stopped_containers"
        echo

        read -p "Remove all stopped containers? (y/N): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            while IFS= read -r container; do
                if [[ -n "$container" ]]; then
                    slog "Removing stopped container: $container"
                    incus delete "$container" 2>/dev/null || true
                fi
            done <<<"$stopped_containers"
        fi
    else
        slog "No stopped containers found"
    fi

    success "Cleanup complete"
}

get_container_ip() {
    local container_name="$1"

    if ! incus info "$container_name" >/dev/null 2>&1; then
        return 1
    fi

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" != "RUNNING" ]]; then
        return 2
    fi

    # Try to get IP from incus list with network info
    local ip
    ip=$(incus list "^${container_name}$" --format csv --columns 4 | head -1 | cut -d',' -f1 | grep -oE '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)

    # If that fails, try getting it from incus info
    if [[ -z "$ip" ]]; then
        ip=$(incus info "$container_name" | grep -A 20 "Network usage:" | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1 | cut -d' ' -f2)
    fi

    # If still no IP, try alternative method
    if [[ -z "$ip" ]]; then
        ip=$(incus exec "$container_name" -- ip -4 addr show | grep -oE 'inet ([0-9]{1,3}\.){3}[0-9]{1,3}' | grep -v '127.0.0.1' | head -1 | cut -d' ' -f2 2>/dev/null)
    fi

    if [[ -z "$ip" ]]; then
        return 3
    fi

    echo "$ip"
    return 0
}

show_container_ip() {
    local container_name="$1"
    check_incus || return 1
    check_container "$container_name" || return 1

    local ip
    ip=$(get_container_ip "$container_name")
    local ret=$?

    case $ret in
    1)
        fail "Container '$container_name' not found"
        return 1
        ;;
    2)
        fail "Container '$container_name' is not running"
        slog "Start it with: $0 start $container_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for container '$container_name'"
        slog "Container may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        echo "$ip"
        return 0
        ;;
    esac
}

ssh_to_container() {
    local container_name="$1"
    local username="${2:-}"
    check_incus || return 1
    check_container "$container_name" || return 1

    local state
    state=$(get_container_state "$container_name")

    if [[ "$state" != "RUNNING" ]]; then
        fail "Container '$container_name' is not running"
        slog "Start it with: $0 start $container_name"
        return 1
    fi

    # Auto-detect username if not provided
    if [[ -z "$username" ]]; then
        # For containers, default to root since they typically don't have cloud-init
        # but also try to detect based on distro patterns
        case "$container_name" in
        ubuntu*) username="ubuntu" ;;
        fedora*) username="fedora" ;;
        centos*) username="centos" ;;
        debian*) username="debian" ;;
        arch*) username="arch" ;;
        alpine*) username="alpine" ;;
        tumbleweed* | tw*) username="opensuse" ;;
        *)
            # For containers, root is often the default
            username="root"
            ;;
        esac

        slog "Auto-detected username: $username (override with: $0 ssh $container_name <username>)"
    fi

    local ip
    ip=$(get_container_ip "$container_name")
    local ret=$?

    case $ret in
    1)
        fail "Container '$container_name' not found"
        return 1
        ;;
    2)
        fail "Container '$container_name' is not running"
        slog "Start it with: $0 start $container_name"
        return 1
        ;;
    3)
        fail "Could not determine IP address for container '$container_name'"
        slog "Container may still be starting up. Try again in a few moments."
        return 1
        ;;
    0)
        slog "Connecting to $container_name ($ip) as $username..."
        ssh "$username@$ip"
        ;;
    esac
}

# Main command handling
if [[ $# -eq 0 ]]; then
    usage
    exit 1
fi

command="$1"
container_name="${2:-}"

case "$command" in
list)
    list_containers
    ;;
status)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    container_status "$container_name"
    ;;
create)
    [[ -z "$container_name" ]] && {
        fail "Distro name required"
        usage
        exit 1
    }
    create_container "$container_name" "${3:-}"
    ;;
start)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    start_container "$container_name"
    ;;
stop)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    stop_container "$container_name"
    ;;
restart)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    restart_container "$container_name"
    ;;
delete)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    delete_container "$container_name"
    ;;
shell)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    enter_shell "$container_name"
    ;;
exec)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    [[ $# -lt 3 ]] && {
        fail "Command required"
        usage
        exit 1
    }
    exec_in_container "$container_name" "${@:3}"
    ;;
ip)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    show_container_ip "$container_name"
    ;;
ssh)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    ssh_to_container "$container_name" "${3:-}"
    ;;
info)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    show_container_info "$container_name"
    ;;
config)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    show_container_config "$container_name"
    ;;
logs)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    show_logs "$container_name" "${3:-}"
    ;;
snapshot)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    create_snapshot "$container_name" "${3:-}"
    ;;
restore)
    [[ -z "$container_name" ]] && {
        fail "Container name required"
        usage
        exit 1
    }
    [[ -z "${3:-}" ]] && {
        fail "Snapshot name required"
        usage
        exit 1
    }
    restore_snapshot "$container_name" "$3"
    ;;
copy)
    [[ -z "$container_name" ]] && {
        fail "Source container name required"
        usage
        exit 1
    }
    [[ -z "${3:-}" ]] && {
        fail "Destination container name required"
        usage
        exit 1
    }
    copy_container "$container_name" "$3"
    ;;
cleanup)
    cleanup_containers
    ;;
--help | -h)
    usage
    ;;
*)
    fail "Unknown command: $command"
    usage
    exit 1
    ;;
esac
