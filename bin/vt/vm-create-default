#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {

    has_cmd virt-install || fail "virt-install is not installed. Please install libvirt and virt-install first."

    slog "Creating 4 libvirt VMs (Ubuntu, Fedora, Arch, Debian)..."

    slog "Creating Ubuntu VM: ubuntu-dev"
    vm-create --distro ubuntu --name ubuntu-dev --release plucky --docker --brew --dotfiles min

    slog "Creating Fedora VM: fedora-dt"
    vm-create --distro fedora --name fedora-dt --dotfiles min

    slog "Creating Arch VM: arch-dt"
    vm-create --distro arch --name arch-dt --dotfiles min

    slog "Creating Debian VM: debian-box"
    vm-create --distro debian --name debian-dt --dotfiles box

    slog "Listing created VMs:"
    virsh list --all

    success "All VMs created successfully!"
    slog "You can access them using: virsh console <vm-name>"
}

main "$@"
