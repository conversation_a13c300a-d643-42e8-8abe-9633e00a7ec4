#!/bin/bash

set -euo pipefail

DOT_DIR=${DOT_DIR:-$HOME/.ilm}

# shellcheck disable=SC1091
source "$DOT_DIR/share/utils"

main() {

    has_cmd incus || fail "Incus is not installed. Please install it first."

    slog "Creating 4 Incus VMs (Ubuntu, Fedora, Arch, Tumbleweed)..."

    slog "Creating Ubuntu VM: ubuntu-vm"
    ivm-create --distro ubuntu --name ubuntu-vm

    slog "Creating Fedora VM: fedora-vm"
    ivm-create --distro fedora --name fedora-vm

    slog "Creating Arch VM: arch-vm"
    ivm-create --distro arch --name arch-vm

    slog "Creating openSUSE Tumbleweed VM: tw-vm"
    ivm-create --distro tw --name tw-vm

    slog "Listing created VMs:"
    ivm list

    success "All VMs created successfully!"
    slog "You can access them using: incus console <vm-name>"
}

main "$@"
